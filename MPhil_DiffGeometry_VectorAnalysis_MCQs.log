This is pdfTeX, Version 3.141592653-2.6-1.40.25 (MiKTeX 24.1) (preloaded format=pdflatex 2025.5.30)  12 JUN 2025 00:29
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./MPhil_DiffGeometry_VectorAnalysis_MCQs.tex
(MPhil_DiffGeometry_VectorAnalysis_MCQs.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@subparagraph=\count192
\c@figure=\count193
\c@table=\count194
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen141
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen142
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count195
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count196
\leftroot@=\count197
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count198
\DOTSCASE@=\count199
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen143
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count266
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count267
\dotsspace@=\muskip16
\c@parentequation=\count268
\dspbrk@lvl=\count269
\tag@help=\toks18
\row@=\count270
\column@=\count271
\maxfields@=\count272
\andhelp@=\toks19
\eqnshift@=\dimen144
\alignsep@=\dimen145
\tagshift@=\dimen146
\tagwidth@=\dimen147
\totwidth@=\dimen148
\lineht@=\dimen149
\@envbody=\toks20
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.st
y
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count273
\Gm@cntv=\count274
\c@Gm@tempcnt=\count275
\Gm@bindingoffset=\dimen150
\Gm@wd@mp=\dimen151
\Gm@odd@mp=\dimen152
\Gm@even@mp=\dimen153
\Gm@layoutwidth=\dimen154
\Gm@layoutheight=\dimen155
\Gm@layouthoffset=\dimen156
\Gm@layoutvoffset=\dimen157
\Gm@dimlist=\toks23

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip53
\enit@outerparindent=\dimen158
\enit@toks=\toks24
\enit@inbox=\box53
\enit@count@id=\count276
\enitdp@description=\count277
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdf
tex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count278
\l__pdf_internal_box=\box54
)
No file MPhil_DiffGeometry_VectorAnalysis_MCQs.aux.
\openout1 = `MPhil_DiffGeometry_VectorAnalysis_MCQs.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 10.
LaTeX Font Info:    ... okay on input line 10.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for U+msa on input line 11.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 11.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (4.7364pt too wide) in paragraph at lines 21--21
[]\OT1/cmr/bx/n/17.28 SECTION A: DIF-FER-EN-TIAL GE-OM-E-TRY (Ques-
 []

[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}] [2]
[3] [4] [5] [6] [7] (MPhil_DiffGeometry_VectorAnalysis_MCQs.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
 ***********
 ) 
Here is how much of TeX's memory you used:
 2639 strings out of 474486
 39918 string characters out of 5743755
 1937542 words of memory out of 5000000
 24939 multiletter control sequences out of 15000+600000
 567483 words of font info for 72 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,5n,65p,269b,240s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcrm
1200.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfon
ts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/publ
ic/amsfonts/cm/cmex10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/t
ype1/public/amsfonts/cm/cmmi12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTe
X/fonts/type1/public/amsfonts/cm/cmmi6.pfb><C:/Users/<USER>/AppData/Local/Progra
ms/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Users/<USER>/AppData/Loca
l/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/AppD
ata/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr17.pfb><C:/Users/<USER>
aya/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr6.pfb><C:/U
sers/inaya/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr8.pf
b><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/
cmsy10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/ams
fonts/cm/cmsy8.pfb>
Output written on MPhil_DiffGeometry_VectorAnalysis_MCQs.pdf (7 pages, 148563 b
ytes).
PDF statistics:
 86 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

